# 🌸 Thesis Checker - Beautiful Flower-Themed Flask Application

A stunning Flask web application with a beautiful flower-themed UI that helps students check their thesis documents for grammar, spelling, punctuation, and potential plagiarism issues.

## ✨ Features

### 🌺 Beautiful User Interface
- **Stunning Landing Page**: Professional landing page with animated elements and floating flowers
- **Flower-Themed Design**: Gorgeous gradient backgrounds and flower emojis throughout
- **Responsive Layout**: Works perfectly on desktop and mobile devices
- **Animated Elements**: Smooth hover effects, gradient animations, and floating flower animations
- **Separate Action Buttons**: Individual buttons for different types of checks
- **Professional Navigation**: Easy navigation between Home, Checker, and About pages

### 🌿 Grammar & Language Checking
- **Advanced Grammar Checking**: Uses LanguageTool when Java is available
- **Simple Grammar Fallback**: Built-in grammar checker when Java is not installed
- **Spelling Correction**: Detects and suggests corrections for common spelling errors
- **Smart Categorization**: Separates issues into spelling, punctuation, and capitalization

### 🌹 Specialized Check Types
- **🌿 Grammar & Spelling Check**: Comprehensive language analysis
- **🌻 Punctuation Check**: Focus specifically on punctuation issues
- **🌷 Capitalization Check**: Verify proper capitalization throughout
- **🌹 Plagiarism Detection**: Check against common academic phrases
- **🌺 Complete Analysis**: Run all checks simultaneously

### 📄 File Support & Processing
- **Multiple Formats**: Accepts .txt, .pdf, and .docx files
- **Smart Text Extraction**: Handles various file encodings and formats
- **File Size Limit**: 16MB maximum file size with clear error messages
- **Automatic Cleanup**: Files are automatically removed after processing

## Installation

1. **Clone or download the repository**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Java (Optional but Recommended)**:
   - For advanced grammar checking, install Java 8 or higher
   - Windows: Download from [Oracle](https://www.oracle.com/java/technologies/downloads/) or use `winget install Oracle.JDK.17`
   - macOS: `brew install openjdk` or download from Oracle
   - Linux: `sudo apt install default-jdk` (Ubuntu/Debian) or `sudo yum install java-11-openjdk` (CentOS/RHEL)

4. **Run the application**:
   ```bash
   python untitled4.py
   ```

5. **Access the application**:
   - Local: http://localhost:5000
   - If ngrok is configured, a public URL will be displayed

## 🌺 Usage

### 🏠 Landing Page Experience
1. **Visit the beautiful landing page** at http://localhost:5000
2. **Explore the features** showcased with animated flower elements
3. **View statistics** and learn about the application's capabilities
4. **Click "🌺 Start Checking Now 🌺"** to begin analyzing your document

### 📝 Document Analysis
1. **Upload your document**: Click "🌺 Choose Your Document 🌺" and select your thesis file (.txt, .pdf, or .docx)
2. **Choose your analysis type** by clicking one of the beautiful flower buttons:
   - **🌿 Check Grammar & Spelling 🌿**: Complete grammar and spelling analysis
   - **🌹 Check Plagiarism 🌹**: Detect potential plagiarism issues
   - **🌻 Check Punctuation 🌻**: Focus on punctuation problems only
   - **🌷 Check Capitalization 🌷**: Check capitalization issues only
   - **🌺 Complete Analysis 🌺**: Run all checks simultaneously
4. **Review the beautiful results** displayed in color-coded cards:
   - 🌿 Grammar & spelling issues with suggestions
   - 📝 Spelling errors with corrections
   - 🌻 Punctuation problems with fixes
   - 🌷 Capitalization issues with proper formatting
   - 🌹 Potential plagiarism matches with similarity percentages
5. **Download corrected text**: Click "🌺 Download Corrected Text 🌺" if available

### 🧭 Navigation
- **🏠 Home**: Beautiful landing page with features and statistics
- **🌺 Checker**: Main application for document analysis
- **ℹ️ About**: Learn more about the application and its features

## Dependencies

- Flask: Web framework
- python-docx: For reading Word documents
- PyPDF2: For reading PDF files
- language-tool-python: Grammar and spell checking
- pyngrok: For creating public tunnels (optional)

## File Support

- **Text files (.txt)**: UTF-8, Latin-1, and CP1252 encodings supported
- **PDF files (.pdf)**: Non-encrypted PDFs only
- **Word documents (.docx)**: Standard Word format

## Error Handling

The application includes comprehensive error handling for:
- Invalid file types
- Corrupted or unreadable files
- Empty files
- File size limits
- Text extraction failures
- Grammar checking errors

## Troubleshooting

### "No java install detected" Error
- **Problem**: LanguageTool requires Java for advanced grammar checking
- **Solution**: Install Java 8 or higher (see installation instructions above)
- **Workaround**: The application will automatically fall back to simple grammar checking

### "Import Error" for dependencies
- **Problem**: Required packages not installed
- **Solution**: Run `pip install -r requirements.txt`

### "Ngrok authentication failed"
- **Problem**: Invalid or expired ngrok token
- **Solution**: Get a new token from [ngrok dashboard](https://dashboard.ngrok.com/get-started/your-authtoken) and replace it in the code
- **Workaround**: The application works fine locally without ngrok

### File upload issues
- **Problem**: File not uploading or processing
- **Solution**: Ensure file is under 16MB and is .txt, .pdf, or .docx format
- **Check**: File is not corrupted or password-protected (for PDFs)

## Notes

- The plagiarism detection is basic and checks against common academic phrases
- For production use, consider implementing more sophisticated plagiarism detection
- The ngrok token in the code should be replaced with your own token
- Files are automatically cleaned up after processing
