# AI Thesis Checker Requirements
# Core Flask and Web Framework
Flask==2.3.3
Werkzeug==2.3.7

# Document Processing
python-docx==0.8.11
PyPDF2==3.0.1

# PDF Generation for Downloads
reportlab==4.0.4

# Advanced Grammar and Language Processing
language-tool-python==2.7.1

# Optional: OpenAI Integration for Enhanced AI Features
# openai==1.3.0

# Optional: Public Access via ngrok
pyngrok==7.0.0

# Additional Utilities
requests==2.31.0
