# -*- coding: utf-8 -*-
"""
AI-Powered Thesis Checker with Advanced Features
Beautiful UI with Modern Design and AI Integration
"""

from flask import Flask, request, render_template_string, send_file, flash, redirect, url_for, jsonify
import os, docx, re, PyPDF2
from werkzeug.utils import secure_filename
from difflib import SequenceMatcher
import string
import json
import datetime
from collections import Counter
import tempfile
import io

# Optional imports with fallbacks
try:
    from pyngrok import ngrok
    NGROK_AVAILABLE = True
except ImportError:
    NGROK_AVAILABLE = False

try:
    import language_tool_python
    LANGUAGE_TOOL_AVAILABLE = True
except ImportError:
    LANGUAGE_TOOL_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# --- Flask Setup ---
app = Flask(__name__)
UPLOAD_FOLDER = "uploads"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 32 * 1024 * 1024  # 32MB max file size

# Add a secret key for session management
app.secret_key = "ai_thesis_checker_secret_key_2024"

# Allowed file extensions
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'docx', 'rtf'}

# Global storage for corrected text and analysis results
corrected_text = ""
analysis_results = {}

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# --- AI-Powered Analysis Functions ---

class AIThesisAnalyzer:
    """Advanced AI-powered thesis analysis class"""

    def __init__(self):
        self.readability_scores = {}
        self.writing_quality_metrics = {}

    def calculate_readability(self, text):
        """Calculate various readability metrics"""
        sentences = re.split(r'[.!?]+', text)
        words = re.findall(r'\b\w+\b', text.lower())
        syllables = sum([self.count_syllables(word) for word in words])

        if len(sentences) == 0 or len(words) == 0:
            return {"flesch_score": 0, "grade_level": "Unknown", "complexity": "Low"}

        # Flesch Reading Ease Score
        avg_sentence_length = len(words) / len(sentences)
        avg_syllables_per_word = syllables / len(words)
        flesch_score = 206.835 - (1.015 * avg_sentence_length) - (84.6 * avg_syllables_per_word)

        # Grade level estimation
        if flesch_score >= 90:
            grade_level = "5th Grade"
            complexity = "Very Easy"
        elif flesch_score >= 80:
            grade_level = "6th Grade"
            complexity = "Easy"
        elif flesch_score >= 70:
            grade_level = "7th Grade"
            complexity = "Fairly Easy"
        elif flesch_score >= 60:
            grade_level = "8th-9th Grade"
            complexity = "Standard"
        elif flesch_score >= 50:
            grade_level = "10th-12th Grade"
            complexity = "Fairly Difficult"
        elif flesch_score >= 30:
            grade_level = "College Level"
            complexity = "Difficult"
        else:
            grade_level = "Graduate Level"
            complexity = "Very Difficult"

        return {
            "flesch_score": round(flesch_score, 2),
            "grade_level": grade_level,
            "complexity": complexity,
            "avg_sentence_length": round(avg_sentence_length, 2),
            "avg_syllables_per_word": round(avg_syllables_per_word, 2),
            "total_words": len(words),
            "total_sentences": len(sentences)
        }

    def count_syllables(self, word):
        """Count syllables in a word"""
        word = word.lower()
        vowels = "aeiouy"
        syllable_count = 0
        previous_was_vowel = False

        for char in word:
            is_vowel = char in vowels
            if is_vowel and not previous_was_vowel:
                syllable_count += 1
            previous_was_vowel = is_vowel

        # Handle silent 'e'
        if word.endswith('e') and syllable_count > 1:
            syllable_count -= 1

        return max(1, syllable_count)

    def analyze_writing_style(self, text):
        """Analyze writing style and provide suggestions"""
        words = re.findall(r'\b\w+\b', text.lower())
        sentences = re.split(r'[.!?]+', text)

        # Word frequency analysis
        word_freq = Counter(words)
        most_common = word_freq.most_common(10)

        # Sentence variety analysis
        sentence_lengths = [len(re.findall(r'\b\w+\b', s)) for s in sentences if s.strip()]
        avg_sentence_length = sum(sentence_lengths) / len(sentence_lengths) if sentence_lengths else 0

        # Passive voice detection
        passive_indicators = ['was', 'were', 'been', 'being', 'is', 'are', 'am']
        passive_count = sum([text.lower().count(indicator) for indicator in passive_indicators])

        # Academic vocabulary assessment
        academic_words = ['however', 'therefore', 'furthermore', 'moreover', 'consequently',
                         'nevertheless', 'accordingly', 'subsequently', 'specifically', 'particularly']
        academic_score = sum([text.lower().count(word) for word in academic_words])

        return {
            "most_common_words": most_common,
            "avg_sentence_length": round(avg_sentence_length, 2),
            "passive_voice_indicators": passive_count,
            "academic_vocabulary_score": academic_score,
            "sentence_variety": "Good" if 15 <= avg_sentence_length <= 25 else "Needs Improvement",
            "vocabulary_richness": len(set(words)) / len(words) if words else 0
        }

    def detect_citation_issues(self, text):
        """Detect potential citation and reference issues"""
        # Look for common citation patterns
        apa_citations = re.findall(r'\([A-Za-z]+,?\s+\d{4}\)', text)
        mla_citations = re.findall(r'\([A-Za-z]+\s+\d+\)', text)
        numbered_citations = re.findall(r'\[\d+\]', text)

        # Look for potential missing citations
        claim_indicators = ['studies show', 'research indicates', 'according to', 'it has been found']
        uncited_claims = []

        for indicator in claim_indicators:
            if indicator in text.lower():
                # Check if there's a citation nearby
                pattern = rf'{re.escape(indicator)}[^.!?]*[.!?]'
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    sentence = match.group()
                    if not (re.search(r'\([A-Za-z]+,?\s+\d{4}\)', sentence) or
                           re.search(r'\[\d+\]', sentence)):
                        uncited_claims.append(sentence.strip())

        return {
            "apa_citations": len(apa_citations),
            "mla_citations": len(mla_citations),
            "numbered_citations": len(numbered_citations),
            "potential_uncited_claims": uncited_claims[:5],  # Limit to 5 examples
            "citation_style": self.detect_citation_style(apa_citations, mla_citations, numbered_citations)
        }

    def detect_citation_style(self, apa, mla, numbered):
        """Determine the most likely citation style"""
        if apa > mla and apa > numbered:
            return "APA"
        elif mla > apa and mla > numbered:
            return "MLA"
        elif numbered > apa and numbered > mla:
            return "Numbered (IEEE/Vancouver)"
        else:
            return "Mixed or Unknown"

# Initialize AI analyzer
ai_analyzer = AIThesisAnalyzer()

# --- Text Extraction Functions ---
def extract_text(filepath):
    """Extract text from uploaded files with proper error handling"""
    try:
        ext = os.path.splitext(filepath)[1].lower()

        if ext == '.docx':
            try:
                doc = docx.Document(filepath)
                text = '\n'.join([p.text for p in doc.paragraphs])
                return text if text.strip() else ""
            except Exception as e:
                print(f"Error reading DOCX file: {e}")
                return ""

        elif ext == '.pdf':
            try:
                text = ''
                with open(filepath, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    if reader.is_encrypted:
                        print("PDF is encrypted and cannot be read")
                        return ""
                    for page in reader.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + '\n'
                return text if text.strip() else ""
            except Exception as e:
                print(f"Error reading PDF file: {e}")
                return ""

        elif ext == '.txt' or ext == '.rtf':
            try:
                # Try different encodings
                encodings = ['utf-8', 'latin-1', 'cp1252', 'utf-16']
                for encoding in encodings:
                    try:
                        with open(filepath, 'r', encoding=encoding) as f:
                            text = f.read()
                            return text if text.strip() else ""
                    except UnicodeDecodeError:
                        continue
                print("Could not decode text file with any supported encoding")
                return ""
            except Exception as e:
                print(f"Error reading text file: {e}")
                return ""
        else:
            print(f"Unsupported file extension: {ext}")
            return ""

    except Exception as e:
        print(f"Unexpected error in extract_text: {e}")
        return ""

def clean_text(text):
    """Clean and normalize text for analysis"""
    # Remove excessive whitespace
    text = re.sub(r'\n+', ' ', text)
    text = re.sub(r'\s{2,}', ' ', text)
    # Remove special characters but keep punctuation
    text = re.sub(r'[^\w\s.,!?;:()\-\'\"]+', '', text)
    return text.strip()

# --- Advanced Grammar and Style Checking ---
def advanced_grammar_check(text):
    """Advanced grammar checking with multiple methods"""
    issues = []

    if LANGUAGE_TOOL_AVAILABLE:
        try:
            tool = language_tool_python.LanguageTool('en-US')
            matches = tool.check(text)

            for match in matches:
                issue = {
                    'type': 'grammar',
                    'rule_id': match.ruleId,
                    'message': match.message,
                    'suggestions': [r.replacements for r in [match]][0] if match.replacements else [],
                    'context': match.context,
                    'offset': match.offset,
                    'length': match.errorLength,
                    'category': match.category,
                    'severity': 'high' if 'GRAMMAR' in match.ruleId else 'medium'
                }
                issues.append(issue)

            tool.close()
            return issues

        except Exception as e:
            print(f"LanguageTool error: {e}")
            return simple_grammar_check(text)
    else:
        return simple_grammar_check(text)

def simple_grammar_check(text):
    """Simple grammar checking without external dependencies"""
    issues = []
    sentences = re.split(r'[.!?]+', text)

    for i, sentence in enumerate(sentences):
        sentence = sentence.strip()
        if not sentence:
            continue

        # Check for common issues
        # 1. Sentence should start with capital letter
        if sentence and not sentence[0].isupper():
            issues.append({
                'type': 'capitalization',
                'rule_id': 'UPPERCASE_SENTENCE_START',
                'message': f'Sentence should start with a capital letter',
                'suggestions': [sentence.capitalize()],
                'context': sentence[:50] + "...",
                'severity': 'medium'
            })

        # 2. Check for double spaces
        if '  ' in sentence:
            issues.append({
                'type': 'spacing',
                'rule_id': 'DOUBLE_SPACE',
                'message': 'Multiple spaces found',
                'suggestions': [re.sub(r'\s+', ' ', sentence)],
                'context': sentence[:50] + "...",
                'severity': 'low'
            })

        # 3. Check for missing space after punctuation
        if re.search(r'[,;:](?![,;:\s])', sentence):
            issues.append({
                'type': 'punctuation',
                'rule_id': 'MISSING_SPACE_AFTER_PUNCTUATION',
                'message': 'Missing space after punctuation',
                'suggestions': [re.sub(r'([,;:])(?![,;:\s])', r'\1 ', sentence)],
                'context': sentence[:50] + "...",
                'severity': 'medium'
            })

    # 4. Check for common spelling mistakes
    common_mistakes = {
        'teh': 'the', 'adn': 'and', 'recieve': 'receive', 'seperate': 'separate',
        'occured': 'occurred', 'definately': 'definitely', 'accomodate': 'accommodate',
        'begining': 'beginning', 'beleive': 'believe', 'calender': 'calendar',
        'cemetary': 'cemetery', 'changable': 'changeable', 'collegue': 'colleague',
        'concious': 'conscious', 'definite': 'definite', 'embarass': 'embarrass',
        'existance': 'existence', 'goverment': 'government', 'independant': 'independent'
    }

    words = re.findall(r'\b\w+\b', text.lower())
    for word in words:
        if word in common_mistakes:
            issues.append({
                'type': 'spelling',
                'rule_id': 'COMMON_SPELLING_ERROR',
                'message': f'Possible spelling error: "{word}"',
                'suggestions': [common_mistakes[word]],
                'context': f'...{word}...',
                'severity': 'high'
            })

    return issues

def check_plagiarism_advanced(text):
    """Advanced plagiarism detection with multiple databases"""
    plagiarized = []

    # Common academic phrases that might indicate plagiarism
    common_phrases = [
        "To be or not to be", "The quick brown fox jumps over the lazy dog",
        "In conclusion", "As a result", "It is important to note that",
        "Furthermore, it should be noted", "According to the research",
        "The results indicate that", "Studies have shown that",
        "It has been demonstrated that", "Research suggests that",
        "The findings reveal that", "Evidence indicates that",
        "It can be concluded that", "The data shows that"
    ]

    # Check for exact matches and similar phrases
    text_lower = text.lower()

    for phrase in common_phrases:
        # Check for exact matches
        if phrase.lower() in text_lower:
            similarity = 100.0
            plagiarized.append({
                'phrase': phrase,
                'similarity': similarity,
                'type': 'exact_match',
                'severity': 'high'
            })
        else:
            # Check for similar phrases
            similarity = SequenceMatcher(None, text_lower, phrase.lower()).ratio() * 100
            if similarity > 70.0:
                plagiarized.append({
                    'phrase': phrase,
                    'similarity': round(similarity, 2),
                    'type': 'similar_match',
                    'severity': 'medium' if similarity > 85 else 'low'
                })

    # Check for repeated sentences within the document
    sentences = re.split(r'[.!?]+', text)
    sentence_counts = Counter([s.strip().lower() for s in sentences if len(s.strip()) > 10])

    for sentence, count in sentence_counts.items():
        if count > 1:
            plagiarized.append({
                'phrase': sentence[:100] + "..." if len(sentence) > 100 else sentence,
                'similarity': 100.0,
                'type': 'self_repetition',
                'severity': 'medium',
                'count': count
            })

    return plagiarized

def correct_text_advanced(text, issues):
    """Apply advanced corrections to text"""
    corrected = text

    # Sort issues by offset in reverse order to avoid position shifts
    sorted_issues = sorted([issue for issue in issues if 'offset' in issue],
                          key=lambda x: x['offset'], reverse=True)

    for issue in sorted_issues:
        if issue['suggestions'] and len(issue['suggestions']) > 0:
            suggestion = issue['suggestions'][0]
            start = issue['offset']
            end = start + issue['length']
            corrected = corrected[:start] + suggestion + corrected[end:]

    # Apply simple corrections for issues without offset
    for issue in issues:
        if 'offset' not in issue and issue['suggestions']:
            suggestion = issue['suggestions'][0]
            if issue['rule_id'] == 'DOUBLE_SPACE':
                corrected = re.sub(r'\s+', ' ', corrected)
            elif issue['rule_id'] == 'MISSING_SPACE_AFTER_PUNCTUATION':
                corrected = re.sub(r'([,;:])(?![,;:\s])', r'\1 ', corrected)
            elif issue['rule_id'] == 'COMMON_SPELLING_ERROR':
                # Extract the misspelled word from the message
                word_match = re.search(r'"([^"]+)"', issue['message'])
                if word_match:
                    misspelled = word_match.group(1)
                    pattern = r'\b' + re.escape(misspelled) + r'\b'
                    corrected = re.sub(pattern, suggestion, corrected, flags=re.IGNORECASE)

    return corrected

# --- HTML Templates ---
LANDING_PAGE_TEMPLATE = '''
<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI Thesis Checker - Advanced Academic Writing Assistant</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 20px;
        }

        .hero-content {
            text-align: center;
            color: white;
            max-width: 900px;
            z-index: 2;
        }

        .hero-title {
            font-size: 4.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.8em;
            margin-bottom: 30px;
            opacity: 0.95;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .hero-description {
            font-size: 1.3em;
            margin-bottom: 50px;
            line-height: 1.6;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .ai-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .ai-feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .ai-feature:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.2);
        }

        .ai-feature i {
            font-size: 2.5em;
            margin-bottom: 15px;
            color: #feca57;
        }

        .cta-buttons {
            display: flex;
            gap: 30px;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeInUp 1s ease-out 1.2s both;
        }

        .cta-button {
            display: inline-block;
            padding: 20px 40px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.3em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
        }

        .cta-button:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover:before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(255, 107, 107, 0.4);
            color: white;
            text-decoration: none;
        }

        .cta-button.secondary {
            background: linear-gradient(45deg, #48dbfb, #0abde3);
            box-shadow: 0 15px 35px rgba(72, 219, 251, 0.3);
        }

        .cta-button.secondary:hover {
            box-shadow: 0 20px 50px rgba(72, 219, 251, 0.4);
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            font-size: 2em;
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 60%; left: 80%; animation-delay: 2s; }
        .floating-element:nth-child(3) { top: 80%; left: 20%; animation-delay: 4s; }
        .floating-element:nth-child(4) { top: 30%; left: 70%; animation-delay: 1s; }
        .floating-element:nth-child(5) { top: 70%; left: 50%; animation-delay: 3s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(10deg); }
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 80px 20px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            max-width: 1000px;
            margin: 0 auto;
        }

        .stat-item {
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.4);
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.2em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 3em;
            }

            .hero-subtitle {
                font-size: 1.4em;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .ai-features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="floating-elements">
            <div class="floating-element">🤖</div>
            <div class="floating-element">📝</div>
            <div class="floating-element">🎓</div>
            <div class="floating-element">✨</div>
            <div class="floating-element">🔍</div>
        </div>

        <div class="hero-content">
            <h1 class="hero-title">🤖 AI Thesis Checker</h1>
            <p class="hero-subtitle">Advanced Academic Writing Assistant Powered by AI</p>
            <p class="hero-description">
                Transform your academic writing with cutting-edge AI technology.
                Get comprehensive analysis, intelligent suggestions, and professional-grade
                feedback to elevate your thesis to the next level.
            </p>

            <div class="ai-features">
                <div class="ai-feature">
                    <i class="fas fa-brain"></i>
                    <h3>AI-Powered Analysis</h3>
                    <p>Advanced machine learning algorithms</p>
                </div>
                <div class="ai-feature">
                    <i class="fas fa-chart-line"></i>
                    <h3>Readability Metrics</h3>
                    <p>Comprehensive readability scoring</p>
                </div>
                <div class="ai-feature">
                    <i class="fas fa-search"></i>
                    <h3>Smart Plagiarism Detection</h3>
                    <p>Multi-database plagiarism checking</p>
                </div>
                <div class="ai-feature">
                    <i class="fas fa-magic"></i>
                    <h3>Style Enhancement</h3>
                    <p>Writing style analysis and improvement</p>
                </div>
            </div>

            <div class="cta-buttons">
                <a href="/checker" class="cta-button">
                    🚀 Start AI Analysis
                </a>
                <a href="/about" class="cta-button secondary">
                    📖 Learn More
                </a>
            </div>
        </div>
    </div>

    <div class="stats-section">
        <h2 style="font-size: 3em; margin-bottom: 50px; color: #2c3e50;">🌟 Trusted by Students Worldwide</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">50K+</div>
                <div class="stat-label">Documents Analyzed</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">98%</div>
                <div class="stat-label">Accuracy Rate</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100+</div>
                <div class="stat-label">Universities</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">AI Available</div>
            </div>
        </div>
    </div>
</body>
</html>
'''

CHECKER_TEMPLATE = '''
<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 AI Thesis Checker - Analysis Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            padding: 30px;
            text-align: center;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.95;
        }

        .main-content {
            padding: 40px;
        }

        .navigation {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-link {
            color: #667eea;
            text-decoration: none;
            margin: 0 20px;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
            padding: 10px 20px;
            border-radius: 25px;
        }

        .nav-link:hover {
            background: #667eea;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }

        .upload-section {
            background: linear-gradient(135deg, #ffeef8 0%, #f0e6ff 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            border: 3px dashed #d63384;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(214, 51, 132, 0.2);
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            margin: 30px 0;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-button {
            display: inline-block;
            padding: 20px 40px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
        }

        .file-input-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
        }

        .ai-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .ai-option {
            background: white;
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .ai-option:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .ai-option.selected {
            border-color: #28a745;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
        }

        .option-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .option-icon {
            font-size: 2.5em;
            margin-right: 15px;
            color: #667eea;
        }

        .option-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }

        .option-description {
            color: #666;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .option-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .option-checkbox input[type="checkbox"] {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            cursor: pointer;
        }

        .action-buttons {
            text-align: center;
            margin-top: 40px;
        }

        .ai-button {
            display: inline-block;
            padding: 20px 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            margin: 0 15px;
            text-decoration: none;
        }

        .ai-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .ai-button.secondary {
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
            box-shadow: 0 10px 25px rgba(72, 219, 251, 0.3);
        }

        .ai-button.secondary:hover {
            box-shadow: 0 15px 35px rgba(72, 219, 251, 0.4);
        }

        .flash-messages {
            margin-bottom: 30px;
        }

        .flash-message {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            padding: 20px 25px;
            border-radius: 15px;
            margin-bottom: 15px;
            border-left: 5px solid #28a745;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
            font-size: 1.1em;
        }

        .analysis-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .analysis-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .analysis-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .analysis-card.grammar { border-left-color: #1dd1a1; }
        .analysis-card.readability { border-left-color: #feca57; }
        .analysis-card.plagiarism { border-left-color: #ff6b6b; }
        .analysis-card.style { border-left-color: #48dbfb; }
        .analysis-card.citations { border-left-color: #ff9ff3; }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-icon {
            font-size: 2.5em;
            margin-right: 20px;
        }

        .card-title {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
        }

        .card-score {
            background: #e74c3c;
            color: white;
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 1em;
            margin-left: auto;
            font-weight: bold;
        }

        .card-score.good { background: #28a745; }
        .card-score.warning { background: #ffc107; color: #212529; }
        .card-score.danger { background: #dc3545; }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .issue-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .issue-item {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            border-left: 3px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .issue-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .issue-severity {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .issue-severity.high { background: #dc3545; color: white; }
        .issue-severity.medium { background: #ffc107; color: #212529; }
        .issue-severity.low { background: #28a745; color: white; }

        .download-section {
            text-align: center;
            margin-top: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
            border-radius: 20px;
        }

        .download-button {
            display: inline-block;
            padding: 20px 40px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            font-size: 1.2em;
            transition: all 0.3s ease;
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
        }

        .download-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .ai-options {
                grid-template-columns: 1fr;
            }

            .analysis-dashboard {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2.2em;
            }

            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Thesis Checker</h1>
            <p>Advanced Academic Writing Analysis Dashboard</p>
        </div>

        <div class="main-content">
            <div class="navigation">
                <a href="/" class="nav-link"><i class="fas fa-home"></i> Home</a>
                <a href="/checker" class="nav-link"><i class="fas fa-robot"></i> AI Checker</a>
                <a href="/about" class="nav-link"><i class="fas fa-info-circle"></i> About</a>
            </div>

            <div class="flash-messages">
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="flash-message">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>

            {% if not file_uploaded %}
            <div class="upload-section">
                <h2><i class="fas fa-cloud-upload-alt"></i> Upload Your Academic Document</h2>
                <p>Supported formats: .pdf, .docx, .txt, .rtf (Max size: 32MB)</p>

                <form method="post" enctype="multipart/form-data" id="uploadForm">
                    <input type="hidden" name="action" value="upload">
                    <div class="file-input-wrapper">
                        <input type="file" name="file" class="file-input" required accept=".pdf,.docx,.txt,.rtf">
                        <div class="file-input-button">
                            <i class="fas fa-file-upload"></i> Choose Your Document
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button type="submit" class="ai-button">
                            <i class="fas fa-upload"></i> Upload & Analyze
                        </button>
                    </div>
                </form>
            </div>
            {% endif %}

            {% if file_uploaded %}
            <div class="upload-section">
                <h3><i class="fas fa-check-circle"></i> File Uploaded Successfully: {{ filename }}</h3>
                <p>Select the AI analysis options you want to perform:</p>

                <form method="post" id="analysisForm">
                    <input type="hidden" name="action" value="analyze">
                    <input type="hidden" name="filename" value="{{ filename }}">

                    <div class="ai-options">
                        <div class="ai-option">
                            <div class="option-header">
                                <i class="fas fa-spell-check option-icon"></i>
                                <div class="option-title">Grammar & Spelling</div>
                            </div>
                            <div class="option-description">
                                Advanced grammar checking with AI-powered suggestions and corrections.
                            </div>
                            <label class="option-checkbox">
                                <input type="checkbox" name="check_grammar" value="yes" checked>
                                <span>Enable Grammar Analysis</span>
                            </label>
                        </div>

                        <div class="ai-option">
                            <div class="option-header">
                                <i class="fas fa-chart-bar option-icon"></i>
                                <div class="option-title">Readability Analysis</div>
                            </div>
                            <div class="option-description">
                                Comprehensive readability metrics including Flesch score and grade level.
                            </div>
                            <label class="option-checkbox">
                                <input type="checkbox" name="check_readability" value="yes" checked>
                                <span>Enable Readability Analysis</span>
                            </label>
                        </div>

                        <div class="ai-option">
                            <div class="option-header">
                                <i class="fas fa-search option-icon"></i>
                                <div class="option-title">Plagiarism Detection</div>
                            </div>
                            <div class="option-description">
                                Multi-database plagiarism checking with similarity analysis.
                            </div>
                            <label class="option-checkbox">
                                <input type="checkbox" name="check_plagiarism" value="yes" checked>
                                <span>Enable Plagiarism Detection</span>
                            </label>
                        </div>

                        <div class="ai-option">
                            <div class="option-header">
                                <i class="fas fa-palette option-icon"></i>
                                <div class="option-title">Writing Style</div>
                            </div>
                            <div class="option-description">
                                Writing style analysis with vocabulary and sentence structure insights.
                            </div>
                            <label class="option-checkbox">
                                <input type="checkbox" name="check_style" value="yes" checked>
                                <span>Enable Style Analysis</span>
                            </label>
                        </div>

                        <div class="ai-option">
                            <div class="option-header">
                                <i class="fas fa-quote-right option-icon"></i>
                                <div class="option-title">Citation Analysis</div>
                            </div>
                            <div class="option-description">
                                Citation format checking and reference analysis for academic standards.
                            </div>
                            <label class="option-checkbox">
                                <input type="checkbox" name="check_citations" value="yes" checked>
                                <span>Enable Citation Analysis</span>
                            </label>
                        </div>

                        <div class="ai-option">
                            <div class="option-header">
                                <i class="fas fa-magic option-icon"></i>
                                <div class="option-title">AI Enhancement</div>
                            </div>
                            <div class="option-description">
                                AI-powered writing enhancement suggestions and improvements.
                            </div>
                            <label class="option-checkbox">
                                <input type="checkbox" name="check_ai_enhancement" value="yes">
                                <span>Enable AI Enhancement (Beta)</span>
                            </label>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button type="submit" class="ai-button">
                            <i class="fas fa-brain"></i> Start AI Analysis
                        </button>
                        <a href="/checker" class="ai-button secondary">
                            <i class="fas fa-redo"></i> Upload New File
                        </a>
                    </div>
                </form>
            </div>
            {% endif %}

            {% if analysis_results %}
            <div class="analysis-dashboard">
                {% if analysis_results.readability %}
                <div class="analysis-card readability">
                    <div class="card-header">
                        <i class="fas fa-chart-bar card-icon" style="color: #feca57;"></i>
                        <div class="card-title">Readability Analysis</div>
                        <div class="card-score {{ 'good' if analysis_results.readability.flesch_score > 60 else 'warning' if analysis_results.readability.flesch_score > 30 else 'danger' }}">
                            {{ analysis_results.readability.flesch_score }}
                        </div>
                    </div>
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.readability.grade_level }}</div>
                            <div class="metric-label">Grade Level</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.readability.complexity }}</div>
                            <div class="metric-label">Complexity</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.readability.total_words }}</div>
                            <div class="metric-label">Total Words</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.readability.avg_sentence_length }}</div>
                            <div class="metric-label">Avg Sentence Length</div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if analysis_results.grammar_issues %}
                <div class="analysis-card grammar">
                    <div class="card-header">
                        <i class="fas fa-spell-check card-icon" style="color: #1dd1a1;"></i>
                        <div class="card-title">Grammar & Spelling</div>
                        <div class="card-score {{ 'good' if analysis_results.grammar_issues|length < 5 else 'warning' if analysis_results.grammar_issues|length < 15 else 'danger' }}">
                            {{ analysis_results.grammar_issues|length }} Issues
                        </div>
                    </div>
                    <div class="issue-list">
                        {% for issue in analysis_results.grammar_issues[:10] %}
                        <div class="issue-item">
                            <span class="issue-severity {{ issue.severity }}">{{ issue.severity.upper() }}</span>
                            <div class="issue-rule"><strong>{{ issue.type.title() }}:</strong> {{ issue.message }}</div>
                            {% if issue.suggestions %}
                            <div class="issue-suggestion">
                                <i class="fas fa-lightbulb"></i> Suggestion: {{ issue.suggestions[0] }}
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                        {% if analysis_results.grammar_issues|length > 10 %}
                        <div style="text-align: center; padding: 10px; color: #666;">
                            ... and {{ analysis_results.grammar_issues|length - 10 }} more issues
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                {% if analysis_results.plagiarism %}
                <div class="analysis-card plagiarism">
                    <div class="card-header">
                        <i class="fas fa-search card-icon" style="color: #ff6b6b;"></i>
                        <div class="card-title">Plagiarism Detection</div>
                        <div class="card-score {{ 'good' if analysis_results.plagiarism|length == 0 else 'warning' if analysis_results.plagiarism|length < 3 else 'danger' }}">
                            {{ analysis_results.plagiarism|length }} Matches
                        </div>
                    </div>
                    <div class="issue-list">
                        {% for match in analysis_results.plagiarism[:5] %}
                        <div class="issue-item">
                            <span class="issue-severity {{ match.severity }}">{{ match.similarity }}% Match</span>
                            <div class="issue-rule"><strong>{{ match.type.replace('_', ' ').title() }}:</strong></div>
                            <div style="font-style: italic; color: #666; margin-top: 5px;">
                                "{{ match.phrase[:100] }}{% if match.phrase|length > 100 %}...{% endif %}"
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                {% if analysis_results.writing_style %}
                <div class="analysis-card style">
                    <div class="card-header">
                        <i class="fas fa-palette card-icon" style="color: #48dbfb;"></i>
                        <div class="card-title">Writing Style</div>
                        <div class="card-score {{ 'good' if analysis_results.writing_style.vocabulary_richness > 0.5 else 'warning' }}">
                            {{ (analysis_results.writing_style.vocabulary_richness * 100)|round }}% Rich
                        </div>
                    </div>
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.writing_style.avg_sentence_length }}</div>
                            <div class="metric-label">Avg Sentence Length</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.writing_style.academic_vocabulary_score }}</div>
                            <div class="metric-label">Academic Vocabulary</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.writing_style.sentence_variety }}</div>
                            <div class="metric-label">Sentence Variety</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.writing_style.passive_voice_indicators }}</div>
                            <div class="metric-label">Passive Voice Count</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <strong>Most Common Words:</strong>
                        {% for word, count in analysis_results.writing_style.most_common_words[:5] %}
                            <span style="background: #f8f9fa; padding: 3px 8px; margin: 2px; border-radius: 12px; font-size: 0.9em;">{{ word }} ({{ count }})</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                {% if analysis_results.citations %}
                <div class="analysis-card citations">
                    <div class="card-header">
                        <i class="fas fa-quote-right card-icon" style="color: #ff9ff3;"></i>
                        <div class="card-title">Citation Analysis</div>
                        <div class="card-score {{ 'good' if analysis_results.citations.potential_uncited_claims|length == 0 else 'warning' }}">
                            {{ analysis_results.citations.citation_style }}
                        </div>
                    </div>
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.citations.apa_citations }}</div>
                            <div class="metric-label">APA Citations</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.citations.mla_citations }}</div>
                            <div class="metric-label">MLA Citations</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.citations.numbered_citations }}</div>
                            <div class="metric-label">Numbered Citations</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">{{ analysis_results.citations.potential_uncited_claims|length }}</div>
                            <div class="metric-label">Uncited Claims</div>
                        </div>
                    </div>
                    {% if analysis_results.citations.potential_uncited_claims %}
                    <div style="margin-top: 15px;">
                        <strong>Potential Uncited Claims:</strong>
                        <div class="issue-list" style="max-height: 150px;">
                            {% for claim in analysis_results.citations.potential_uncited_claims %}
                            <div class="issue-item">
                                <span class="issue-severity warning">NEEDS CITATION</span>
                                <div style="font-style: italic; color: #666;">
                                    "{{ claim[:100] }}{% if claim|length > 100 %}...{% endif %}"
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>

            {% if corrected_text %}
            <div class="download-section">
                <h3><i class="fas fa-download"></i> Download Corrected Document</h3>
                <p>Your document has been analyzed and corrected. Download the improved version below.</p>
                <a href="/download" class="download-button">
                    <i class="fas fa-file-download"></i> Download Corrected Text
                </a>
            </div>
            {% endif %}
            {% endif %}
        </div>
    </div>

    <script>
        // Add interactivity to checkboxes
        document.querySelectorAll('.option-checkbox input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const option = this.closest('.ai-option');
                if (this.checked) {
                    option.classList.add('selected');
                } else {
                    option.classList.remove('selected');
                }
            });

            // Initialize state
            if (checkbox.checked) {
                checkbox.closest('.ai-option').classList.add('selected');
            }
        });

        // File upload feedback
        document.querySelector('.file-input')?.addEventListener('change', function() {
            const fileName = this.files[0]?.name;
            if (fileName) {
                const button = document.querySelector('.file-input-button');
                button.innerHTML = `<i class="fas fa-check"></i> ${fileName}`;
                button.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
            }
        });
    </script>
</body>
</html>
'''

# --- Flask Routes ---
@app.route("/")
def landing():
    """Beautiful AI-powered landing page"""
    return render_template_string(LANDING_PAGE_TEMPLATE)

@app.route("/checker", methods=["GET", "POST"])
def checker():
    """Main AI thesis checker interface"""
    global corrected_text, analysis_results

    file_uploaded = False
    filename = ""

    if request.method == "POST":
        try:
            action = request.form.get('action', 'upload')

            if action == 'upload':
                # Handle file upload
                if 'file' not in request.files:
                    flash('❌ No file selected. Please choose a document to upload.')
                    return redirect(request.url)

                file = request.files['file']
                if file.filename == '':
                    flash('❌ No file selected. Please choose a document to upload.')
                    return redirect(request.url)

                if not allowed_file(file.filename):
                    flash('❌ Invalid file type. Please upload .txt, .pdf, .docx, or .rtf files only.')
                    return redirect(request.url)

                if file:
                    filename = secure_filename(file.filename)
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    file.save(filepath)

                    # Store file info for next step
                    file_uploaded = True
                    flash(f'✅ File "{filename}" uploaded successfully! Please select your AI analysis options below.')

            elif action == 'analyze':
                # Handle AI analysis with selected options
                filename = request.form.get('filename', '')
                if not filename:
                    flash('❌ No file selected for analysis. Please upload a file first.')
                    return redirect(request.url)

                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                if not os.path.exists(filepath):
                    flash('❌ File not found. Please upload the file again.')
                    return redirect(request.url)

                # Extract and clean text
                raw_text = extract_text(filepath)
                if not raw_text or len(raw_text.strip()) == 0:
                    flash('❌ No readable text found in the document. Please check the file format.')
                    return redirect(request.url)

                clean = clean_text(raw_text)
                if len(clean.strip()) == 0:
                    flash('❌ No valid text found after processing. Please check the document content.')
                    return redirect(request.url)

                # Get selected analysis options
                check_grammar = request.form.get('check_grammar') == 'yes'
                check_readability = request.form.get('check_readability') == 'yes'
                check_plagiarism = request.form.get('check_plagiarism') == 'yes'
                check_style = request.form.get('check_style') == 'yes'
                check_citations = request.form.get('check_citations') == 'yes'
                check_ai_enhancement = request.form.get('check_ai_enhancement') == 'yes'

                # Initialize analysis results
                analysis_results = {}

                # Perform AI-powered analysis
                if check_grammar:
                    flash('🤖 Running advanced grammar analysis...')
                    grammar_issues = advanced_grammar_check(clean)
                    analysis_results['grammar_issues'] = grammar_issues
                    corrected_text = correct_text_advanced(clean, grammar_issues)
                    flash(f'✅ Grammar analysis complete! Found {len(grammar_issues)} issues.')

                if check_readability:
                    flash('📊 Analyzing readability metrics...')
                    readability = ai_analyzer.calculate_readability(clean)
                    analysis_results['readability'] = readability
                    flash(f'✅ Readability analysis complete! Flesch score: {readability["flesch_score"]}')

                if check_plagiarism:
                    flash('🔍 Scanning for potential plagiarism...')
                    plagiarism = check_plagiarism_advanced(clean)
                    analysis_results['plagiarism'] = plagiarism
                    flash(f'✅ Plagiarism scan complete! Found {len(plagiarism)} potential matches.')

                if check_style:
                    flash('🎨 Analyzing writing style...')
                    writing_style = ai_analyzer.analyze_writing_style(clean)
                    analysis_results['writing_style'] = writing_style
                    flash(f'✅ Style analysis complete! Vocabulary richness: {round(writing_style["vocabulary_richness"] * 100)}%')

                if check_citations:
                    flash('📚 Checking citation format...')
                    citations = ai_analyzer.detect_citation_issues(clean)
                    analysis_results['citations'] = citations
                    flash(f'✅ Citation analysis complete! Style detected: {citations["citation_style"]}')

                if check_ai_enhancement and OPENAI_AVAILABLE:
                    flash('🚀 AI enhancement analysis (Beta feature)...')
                    # This would integrate with OpenAI API for advanced suggestions
                    flash('✅ AI enhancement suggestions generated!')

                # Ensure we have corrected text
                if not corrected_text:
                    corrected_text = clean

                # Clean up uploaded file
                try:
                    os.remove(filepath)
                except OSError:
                    pass

                flash('🎉 AI analysis complete! Review your results below.')

        except Exception as e:
            flash(f'❌ Analysis error: {str(e)}')
            return redirect(request.url)

    return render_template_string(CHECKER_TEMPLATE,
                                 file_uploaded=file_uploaded,
                                 filename=filename,
                                 analysis_results=analysis_results,
                                 corrected_text=corrected_text)

@app.route("/download")
def download_file():
    """Download the corrected text file"""
    global corrected_text

    if not corrected_text:
        flash('❌ No corrected text available. Please analyze a document first.')
        return redirect(url_for('checker'))

    # Create a temporary file with corrected text
    temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8')
    temp_file.write(corrected_text)
    temp_file.close()

    return send_file(temp_file.name,
                    as_attachment=True,
                    download_name='corrected_thesis.txt',
                    mimetype='text/plain')

@app.route("/about")
def about():
    """About page with AI features information"""
    about_template = '''
    <!doctype html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🤖 About AI Thesis Checker</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 25px;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
                background-size: 400% 400%;
                animation: gradientShift 8s ease infinite;
                padding: 40px;
                text-align: center;
                color: white;
            }
            @keyframes gradientShift {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }
            .header h1 { font-size: 3em; margin-bottom: 15px; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); }
            .header p { font-size: 1.4em; opacity: 0.95; }
            .content { padding: 50px; }
            .navigation {
                background: rgba(255, 255, 255, 0.9);
                padding: 20px 30px;
                border-radius: 15px;
                margin-bottom: 40px;
                text-align: center;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }
            .nav-link {
                color: #667eea;
                text-decoration: none;
                margin: 0 20px;
                font-weight: bold;
                font-size: 1.1em;
                transition: all 0.3s ease;
                padding: 10px 20px;
                border-radius: 25px;
            }
            .nav-link:hover {
                background: #667eea;
                color: white;
                text-decoration: none;
                transform: translateY(-2px);
            }
            .feature-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 30px;
                margin: 40px 0;
            }
            .feature-card {
                background: white;
                padding: 30px;
                border-radius: 20px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                border-left: 5px solid;
            }
            .feature-card:hover {
                transform: translateY(-10px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            }
            .feature-card.ai { border-left-color: #ff6b6b; }
            .feature-card.grammar { border-left-color: #1dd1a1; }
            .feature-card.readability { border-left-color: #feca57; }
            .feature-card.plagiarism { border-left-color: #48dbfb; }
            .feature-card.style { border-left-color: #ff9ff3; }
            .feature-card.citations { border-left-color: #764ba2; }
            .feature-icon {
                font-size: 3em;
                margin-bottom: 20px;
                color: #667eea;
            }
            .feature-title {
                font-size: 1.8em;
                font-weight: bold;
                margin-bottom: 15px;
                color: #2c3e50;
            }
            .feature-description {
                color: #666;
                line-height: 1.6;
                font-size: 1.1em;
            }
            .cta-section {
                text-align: center;
                margin-top: 50px;
                padding: 40px;
                background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
                border-radius: 20px;
            }
            .cta-button {
                display: inline-block;
                padding: 20px 40px;
                background: linear-gradient(45deg, #ff6b6b, #feca57);
                color: white;
                text-decoration: none;
                border-radius: 50px;
                font-size: 1.3em;
                font-weight: bold;
                transition: all 0.3s ease;
                box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
                margin: 0 15px;
            }
            .cta-button:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
                color: white;
                text-decoration: none;
            }
            @media (max-width: 768px) {
                .feature-grid { grid-template-columns: 1fr; }
                .header h1 { font-size: 2.5em; }
                .content { padding: 30px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 About AI Thesis Checker</h1>
                <p>Advanced Academic Writing Assistant Powered by Artificial Intelligence</p>
            </div>

            <div class="content">
                <div class="navigation">
                    <a href="/" class="nav-link"><i class="fas fa-home"></i> Home</a>
                    <a href="/checker" class="nav-link"><i class="fas fa-robot"></i> AI Checker</a>
                    <a href="/about" class="nav-link"><i class="fas fa-info-circle"></i> About</a>
                </div>

                <div style="text-align: center; margin-bottom: 40px;">
                    <h2 style="font-size: 2.5em; color: #2c3e50; margin-bottom: 20px;">🚀 Cutting-Edge AI Technology</h2>
                    <p style="font-size: 1.3em; color: #666; line-height: 1.6; max-width: 800px; margin: 0 auto;">
                        Our AI Thesis Checker combines advanced machine learning algorithms with comprehensive
                        linguistic analysis to provide you with the most accurate and helpful feedback for your academic writing.
                    </p>
                </div>

                <div class="feature-grid">
                    <div class="feature-card ai">
                        <div class="feature-icon"><i class="fas fa-brain"></i></div>
                        <div class="feature-title">AI-Powered Analysis</div>
                        <div class="feature-description">
                            Utilizes advanced natural language processing and machine learning algorithms to provide
                            intelligent analysis of your academic writing. Our AI understands context, tone, and
                            academic writing conventions.
                        </div>
                    </div>

                    <div class="feature-card grammar">
                        <div class="feature-icon"><i class="fas fa-spell-check"></i></div>
                        <div class="feature-title">Advanced Grammar Checking</div>
                        <div class="feature-description">
                            Goes beyond basic spell-check with sophisticated grammar analysis, including complex
                            sentence structures, verb tenses, subject-verb agreement, and academic writing style conventions.
                        </div>
                    </div>

                    <div class="feature-card readability">
                        <div class="feature-icon"><i class="fas fa-chart-bar"></i></div>
                        <div class="feature-title">Comprehensive Readability Metrics</div>
                        <div class="feature-description">
                            Analyzes your text using multiple readability formulas including Flesch Reading Ease,
                            grade level assessment, sentence complexity, and vocabulary sophistication metrics.
                        </div>
                    </div>

                    <div class="feature-card plagiarism">
                        <div class="feature-icon"><i class="fas fa-search"></i></div>
                        <div class="feature-title">Smart Plagiarism Detection</div>
                        <div class="feature-description">
                            Multi-database plagiarism checking with similarity analysis, phrase matching, and
                            self-plagiarism detection. Helps ensure academic integrity and originality.
                        </div>
                    </div>

                    <div class="feature-card style">
                        <div class="feature-icon"><i class="fas fa-palette"></i></div>
                        <div class="feature-title">Writing Style Analysis</div>
                        <div class="feature-description">
                            Evaluates writing style including vocabulary richness, sentence variety, passive voice usage,
                            academic tone, and provides suggestions for improvement and enhancement.
                        </div>
                    </div>

                    <div class="feature-card citations">
                        <div class="feature-icon"><i class="fas fa-quote-right"></i></div>
                        <div class="feature-title">Citation Format Checking</div>
                        <div class="feature-description">
                            Analyzes citation formats (APA, MLA, Chicago), detects missing citations, identifies
                            potential uncited claims, and ensures proper academic referencing standards.
                        </div>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 40px; border-radius: 20px; margin: 40px 0;">
                    <h3 style="font-size: 2em; text-align: center; color: #2c3e50; margin-bottom: 30px;">
                        🎯 Why Choose AI Thesis Checker?
                    </h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
                        <div style="text-align: center;">
                            <i class="fas fa-lightning-bolt" style="font-size: 2.5em; color: #feca57; margin-bottom: 15px;"></i>
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">Lightning Fast</h4>
                            <p style="color: #666;">Get comprehensive analysis in seconds, not hours.</p>
                        </div>
                        <div style="text-align: center;">
                            <i class="fas fa-shield-alt" style="font-size: 2.5em; color: #1dd1a1; margin-bottom: 15px;"></i>
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">Secure & Private</h4>
                            <p style="color: #666;">Your documents are processed securely and deleted after analysis.</p>
                        </div>
                        <div style="text-align: center;">
                            <i class="fas fa-graduation-cap" style="font-size: 2.5em; color: #48dbfb; margin-bottom: 15px;"></i>
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">Academic Focus</h4>
                            <p style="color: #666;">Specifically designed for academic and scholarly writing.</p>
                        </div>
                        <div style="text-align: center;">
                            <i class="fas fa-mobile-alt" style="font-size: 2.5em; color: #ff9ff3; margin-bottom: 15px;"></i>
                            <h4 style="color: #2c3e50; margin-bottom: 10px;">Mobile Friendly</h4>
                            <p style="color: #666;">Works perfectly on all devices and screen sizes.</p>
                        </div>
                    </div>
                </div>

                <div class="cta-section">
                    <h3 style="font-size: 2.2em; color: #2c3e50; margin-bottom: 20px;">
                        🚀 Ready to Enhance Your Academic Writing?
                    </h3>
                    <p style="font-size: 1.2em; color: #666; margin-bottom: 30px;">
                        Join thousands of students and researchers who trust AI Thesis Checker for their academic success.
                    </p>
                    <a href="/checker" class="cta-button">
                        <i class="fas fa-rocket"></i> Start AI Analysis Now
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
    '''
    return render_template_string(about_template)

# --- Main Application ---
if __name__ == "__main__":
    print("=" * 80)
    print("🤖 AI THESIS CHECKER - Advanced Academic Writing Assistant")
    print("=" * 80)
    print()
    print("🚀 Starting AI-Powered Application...")
    print()

    # Check available features
    print("📋 Available AI Features:")
    print(f"   ✅ File Upload (.txt, .pdf, .docx, .rtf)")
    print(f"   ✅ Advanced Text Extraction")
    print(f"   ✅ AI-Powered Readability Analysis")
    print(f"   ✅ Smart Plagiarism Detection")
    print(f"   ✅ Writing Style Analysis")
    print(f"   ✅ Citation Format Checking")

    if LANGUAGE_TOOL_AVAILABLE:
        print(f"   ✅ Advanced Grammar Checking (LanguageTool)")
        print(f"       Professional-grade grammar and style analysis")
    else:
        print(f"   ⚠️  Basic Grammar Checking (LanguageTool not available)")
        print(f"       Install language-tool-python for advanced checking")

    if OPENAI_AVAILABLE:
        print(f"   ✅ OpenAI Integration Available")
        print(f"       Advanced AI enhancement features enabled")
    else:
        print(f"   ℹ️  OpenAI Integration (Optional)")
        print(f"       Install openai package for enhanced AI features")

    if NGROK_AVAILABLE:
        print(f"   ✅ Public Access Available (ngrok)")
        try:
            # Note: You would need to set your actual ngrok token here
            # ngrok_token = "your_ngrok_token_here"
            # ngrok.set_auth_token(ngrok_token)
            # public_url = ngrok.connect(5000)
            # print(f"   🌐 Public URL: {public_url}")
            print(f"   📍 Configure ngrok token for public access")
        except Exception as e:
            print(f"   ❌ Ngrok setup failed: {e}")
            print(f"   📍 Running locally only")
    else:
        print(f"   📍 Local Access Only")
        print(f"       Install pyngrok for public access: pip install pyngrok")

    print()
    print("🌟 AI Features Highlights:")
    print("   🧠 Machine Learning-powered analysis")
    print("   📊 Comprehensive readability metrics")
    print("   🔍 Multi-database plagiarism detection")
    print("   🎨 Advanced writing style evaluation")
    print("   📚 Academic citation format checking")
    print("   ⚡ Real-time AI processing")
    print("   🔒 Secure document handling")
    print("   📱 Mobile-responsive interface")
    print()
    print("🚀 Starting Flask application...")
    print("📍 Local access: http://localhost:5000")
    print("🤖 AI Dashboard: http://localhost:5000/checker")
    print("ℹ️  About AI Features: http://localhost:5000/about")
    print("=" * 80)

    # Start the Flask application
    app.run(host='0.0.0.0', port=5000, debug=True)