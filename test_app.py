from flask import Flask, request, render_template_string, flash, redirect, url_for
import os
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.secret_key = "test_secret_key"
UPLOAD_FOLDER = "uploads"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

ALLOWED_EXTENSIONS = {'txt', 'pdf', 'docx'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

HTML_TEMPLATE = '''
<!doctype html>
<html>
<head>
  <title>Thesis Checker Test</title>
  <style>
    body { font-family: Arial; background: #f4f4fc; padding: 40px; }
    h2 { color: #4b0082; }
    input[type=file], input[type=submit] {
        padding: 10px;
        background: #6a1b9a;
        color: white;
        border: none;
        border-radius: 5px;
        margin-top: 10px;
        display: inline-block;
        cursor: pointer;
    }
  </style>
</head>
<body>
  {% with messages = get_flashed_messages() %}
    {% if messages %}
      <ul style="background: #e8d5f0; padding: 10px; border-radius: 5px;">
      {% for message in messages %}
        <li>{{ message }}</li>
      {% endfor %}
      </ul>
    {% endif %}
  {% endwith %}
  
  <h2>🌸 Test Upload (.pdf, .docx, .txt)</h2>
  <form method="post" enctype="multipart/form-data">
    <input type="file" name="file" required><br>
    <input type="submit" value="🌼 Test Upload">
  </form>
  
  {% if filename %}
    <h3>✅ File uploaded successfully: {{ filename }}</h3>
  {% endif %}
</body>
</html>
'''

@app.route("/", methods=["GET", "POST"])
def index():
    filename = None
    
    if request.method == "POST":
        if 'file' not in request.files:
            flash('No file part')
            return redirect(request.url)
            
        file = request.files['file']
        if file.filename == '':
            flash('No selected file')
            return redirect(request.url)
            
        if not allowed_file(file.filename):
            flash('Invalid file type. Please upload .txt, .pdf, or .docx files only.')
            return redirect(request.url)
            
        if file:
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            flash(f'File {filename} uploaded successfully!')
            
            # Clean up the file
            try:
                os.remove(filepath)
            except OSError:
                pass
    
    return render_template_string(HTML_TEMPLATE, filename=filename)

if __name__ == "__main__":
    print(" * Starting test Flask application...")
    print(" * Local access: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
