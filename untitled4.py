# -*- coding: utf-8 -*-
"""Untitled4.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1PwQFl2ULDtMz9_yvruXzi40c7dZY2NGP
"""

from flask import Flask, request, render_template_string, send_file, flash, redirect, url_for
import os, docx, re, PyPDF2
from werkzeug.utils import secure_filename
from difflib import SequenceMatcher
import string

# Optional imports
try:
    from pyngrok import ngrok
    NGROK_AVAILABLE = True
except ImportError:
    NGROK_AVAILABLE = False

try:
    import language_tool_python
    LANGUAGE_TOOL_AVAILABLE = True
except ImportError:
    LANGUAGE_TOOL_AVAILABLE = False

# Check for Java availability (simplified check)
JAVA_AVAILABLE = False
# We'll check Java availability when actually needed to avoid startup delays

# --- Flask Setup ---
app = Flask(__name__)
UPLOAD_FOLDER = "uploads"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Add a secret key for session management
app.secret_key = "thesis_checker_secret_key"

# Allowed file extensions
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'docx'}

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# --- HTML Template ---
HTML_TEMPLATE = '''
<!doctype html>
<html>
<head>
  <title>🌸 Thesis Checker - Beautiful Flower Theme</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .header {
        background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
        background-size: 400% 400%;
        animation: gradientShift 8s ease infinite;
        padding: 30px;
        text-align: center;
        color: white;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .header p {
        font-size: 1.2em;
        opacity: 0.9;
    }

    .main-content {
        padding: 40px;
    }

    .upload-section {
        background: linear-gradient(135deg, #ffeef8 0%, #f0e6ff 100%);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        border: 2px dashed #d63384;
        text-align: center;
        transition: all 0.3s ease;
    }

    .upload-section:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(214, 51, 132, 0.2);
    }

    .file-input-wrapper {
        position: relative;
        display: inline-block;
        margin: 20px 0;
    }

    .file-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .file-input-button {
        display: inline-block;
        padding: 15px 30px;
        background: linear-gradient(45deg, #ff6b6b, #feca57);
        color: white;
        border-radius: 50px;
        font-size: 1.1em;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
    }

    .file-input-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    }

    .buttons-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-top: 30px;
    }

    .flower-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 20px;
        border-radius: 15px;
        font-size: 1.1em;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
    }

    .flower-button:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .flower-button:hover:before {
        left: 100%;
    }

    .flower-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    }

    .flower-button.plagiarism {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
    }

    .flower-button.punctuation {
        background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        box-shadow: 0 5px 15px rgba(254, 202, 87, 0.3);
    }

    .flower-button.capitalization {
        background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        box-shadow: 0 5px 15px rgba(72, 219, 251, 0.3);
    }

    .flower-button.grammar {
        background: linear-gradient(135deg, #1dd1a1 0%, #10ac84 100%);
        box-shadow: 0 5px 15px rgba(29, 209, 161, 0.3);
    }

    .flower-button.secondary {
        background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        box-shadow: 0 5px 15px rgba(72, 219, 251, 0.3);
    }

    .file-uploaded-section {
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
        border-radius: 15px;
        padding: 30px;
        margin-top: 30px;
        text-align: center;
        border: 2px solid #28a745;
    }

    .check-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 30px 0;
        text-align: left;
    }

    .check-option {
        background: white;
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .check-option:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .check-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-size: 1.1em;
        font-weight: bold;
    }

    .check-label input[type="checkbox"] {
        display: none;
    }

    .checkmark {
        font-size: 2em;
        margin-right: 15px;
        transition: all 0.3s ease;
    }

    .check-label input[type="checkbox"]:checked + .checkmark {
        transform: scale(1.2);
        filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.5));
    }

    .check-text {
        color: #2c3e50;
    }

    .results-section {
        margin-top: 30px;
    }

    .result-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-left: 5px solid;
        transition: all 0.3s ease;
    }

    .result-card:hover {
        transform: translateX(10px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .result-card.grammar-card { border-left-color: #1dd1a1; }
    .result-card.spelling-card { border-left-color: #ff6b6b; }
    .result-card.punctuation-card { border-left-color: #feca57; }
    .result-card.capitalization-card { border-left-color: #48dbfb; }
    .result-card.plagiarism-card { border-left-color: #ee5a24; }

    .result-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .result-icon {
        font-size: 2em;
        margin-right: 15px;
    }

    .result-title {
        font-size: 1.5em;
        font-weight: bold;
        color: #2c3e50;
    }

    .result-count {
        background: #e74c3c;
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.9em;
        margin-left: auto;
    }

    .issue-list {
        list-style: none;
        padding: 0;
    }

    .issue-item {
        background: #f8f9fa;
        margin: 10px 0;
        padding: 15px;
        border-radius: 10px;
        border-left: 3px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .issue-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .issue-rule {
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
    }

    .issue-message {
        color: #6c757d;
        margin-bottom: 8px;
    }

    .issue-suggestion {
        color: #28a745;
        font-style: italic;
        font-size: 0.9em;
    }

    .download-section {
        text-align: center;
        margin-top: 30px;
        padding: 20px;
        background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
        border-radius: 15px;
    }

    .download-button {
        display: inline-block;
        padding: 15px 30px;
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
    }

    .download-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .flash-messages {
        margin-bottom: 20px;
    }

    .flash-message {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 10px;
        border-left: 4px solid #28a745;
        box-shadow: 0 3px 10px rgba(40, 167, 69, 0.2);
    }

    .navigation {
        background: rgba(255, 255, 255, 0.9);
        padding: 15px 30px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }

    .nav-link {
        color: #667eea;
        text-decoration: none;
        margin: 0 15px;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .nav-link:hover {
        color: #764ba2;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .buttons-container {
            grid-template-columns: 1fr;
        }

        .header h1 {
            font-size: 2em;
        }

        .main-content {
            padding: 20px;
        }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🌸 Thesis Checker 🌸</h1>
      <p>Beautiful AI-Powered Grammar & Plagiarism Detection</p>
    </div>

    <div class="main-content">
      <div class="navigation">
        <a href="/" class="nav-link">🏠 Home</a>
        <a href="/checker" class="nav-link">🌺 Checker</a>
        <a href="/about" class="nav-link">ℹ️ About</a>
      </div>

      <div class="flash-messages">
        {% with messages = get_flashed_messages() %}
          {% if messages %}
            {% for message in messages %}
              <div class="flash-message">{{ message }}</div>
            {% endfor %}
          {% endif %}
        {% endwith %}
      </div>

      <div class="upload-section">
        <h2>📄 Upload Your Thesis Document</h2>
        <p>Supported formats: .pdf, .docx, .txt (Max size: 16MB)</p>

        <form method="post" enctype="multipart/form-data" id="uploadForm">
          <div class="file-input-wrapper">
            <input type="file" name="file" class="file-input" required accept=".pdf,.docx,.txt">
            <div class="file-input-button">
              🌺 Choose Your Document 🌺
            </div>
          </div>

          <div class="buttons-container">
            <button type="submit" name="check_type" value="grammar" class="flower-button grammar">
              � Check Grammar & Spelling 🌿
            </button>

            <button type="submit" name="check_type" value="plagiarism" class="flower-button plagiarism">
              🌹 Check Plagiarism 🌹
            </button>

            <button type="submit" name="check_type" value="punctuation" class="flower-button punctuation">
              🌻 Check Punctuation 🌻
            </button>

            <button type="submit" name="check_type" value="capitalization" class="flower-button capitalization">
              🌷 Check Capitalization 🌷
            </button>

            <button type="submit" name="check_type" value="all" class="flower-button">
              🌺 Complete Analysis 🌺
            </button>
          </div>
        </form>
      </div>

      <div class="results-section">
        {% if issues %}
          <div class="result-card grammar-card">
            <div class="result-header">
              <div class="result-icon">🌿</div>
              <div class="result-title">Grammar & Spelling Issues</div>
              <div class="result-count">{{ issues|length }}</div>
            </div>
            <ul class="issue-list">
            {% for i in issues[:10] %}
              <li class="issue-item">
                <div class="issue-rule">{{ i.ruleId if i.ruleId else 'Grammar Issue' }}</div>
                <div class="issue-message">{{ i.message }}</div>
                {% if i.replacements %}
                  <div class="issue-suggestion">💡 Suggestion: {{ i.replacements }}</div>
                {% endif %}
              </li>
            {% endfor %}
            </ul>
          </div>
        {% endif %}

        {% if spelling %}
          <div class="result-card spelling-card">
            <div class="result-header">
              <div class="result-icon">📝</div>
              <div class="result-title">Spelling Issues</div>
              <div class="result-count">{{ spelling|length }}</div>
            </div>
            <ul class="issue-list">
            {% for i in spelling[:5] %}
              <li class="issue-item">
                <div class="issue-rule">Spelling Error</div>
                <div class="issue-message">{{ i.message }}</div>
                {% if i.replacements %}
                  <div class="issue-suggestion">💡 Suggestion: {{ i.replacements }}</div>
                {% endif %}
              </li>
            {% endfor %}
            </ul>
          </div>
        {% endif %}

        {% if punctuation %}
          <div class="result-card punctuation-card">
            <div class="result-header">
              <div class="result-icon">🌻</div>
              <div class="result-title">Punctuation Issues</div>
              <div class="result-count">{{ punctuation|length }}</div>
            </div>
            <ul class="issue-list">
            {% for i in punctuation[:5] %}
              <li class="issue-item">
                <div class="issue-rule">{{ i.ruleId if i.ruleId else 'Punctuation Issue' }}</div>
                <div class="issue-message">{{ i.message }}</div>
                {% if i.replacements %}
                  <div class="issue-suggestion">💡 Suggestion: {{ i.replacements }}</div>
                {% endif %}
              </li>
            {% endfor %}
            </ul>
          </div>
        {% endif %}

        {% if capitalization %}
          <div class="result-card capitalization-card">
            <div class="result-header">
              <div class="result-icon">🌷</div>
              <div class="result-title">Capitalization Issues</div>
              <div class="result-count">{{ capitalization|length }}</div>
            </div>
            <ul class="issue-list">
            {% for i in capitalization[:5] %}
              <li class="issue-item">
                <div class="issue-rule">{{ i.ruleId if i.ruleId else 'Capitalization Issue' }}</div>
                <div class="issue-message">{{ i.message }}</div>
                {% if i.replacements %}
                  <div class="issue-suggestion">💡 Suggestion: {{ i.replacements }}</div>
                {% endif %}
              </li>
            {% endfor %}
            </ul>
          </div>
        {% endif %}

        {% if plagiarized %}
          <div class="result-card plagiarism-card">
            <div class="result-header">
              <div class="result-icon">🌹</div>
              <div class="result-title">Possible Plagiarism Detected</div>
              <div class="result-count">{{ plagiarized|length }}</div>
            </div>
            <ul class="issue-list">
            {% for text, similarity in plagiarized %}
              <li class="issue-item">
                <div class="issue-rule">{{ similarity|round(2) }}% Match</div>
                <div class="issue-message">"{{ text }}"</div>
                <div class="issue-suggestion">⚠️ Please review for originality</div>
              </li>
            {% endfor %}
            </ul>
          </div>
        {% endif %}

        {% if issues or plagiarized %}
          <div class="download-section">
            <h3>📥 Download Results</h3>
            <a href="/download" class="download-button">
              🌺 Download Corrected Text 🌺
            </a>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</body>
</html>
'''

# --- Utilities ---
def extract_text(filepath):
    """Extract text from uploaded files with proper error handling"""
    try:
        ext = os.path.splitext(filepath)[1].lower()

        if ext == '.docx':
            try:
                doc = docx.Document(filepath)
                text = '\n'.join([p.text for p in doc.paragraphs])
                return text if text.strip() else ""
            except Exception as e:
                print(f"Error reading DOCX file: {e}")
                return ""

        elif ext == '.pdf':
            try:
                text = ''
                with open(filepath, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    if reader.is_encrypted:
                        print("PDF is encrypted and cannot be read")
                        return ""
                    for page in reader.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + '\n'
                return text if text.strip() else ""
            except Exception as e:
                print(f"Error reading PDF file: {e}")
                return ""

        elif ext == '.txt':
            try:
                # Try different encodings
                encodings = ['utf-8', 'latin-1', 'cp1252']
                for encoding in encodings:
                    try:
                        with open(filepath, 'r', encoding=encoding) as f:
                            text = f.read()
                            return text if text.strip() else ""
                    except UnicodeDecodeError:
                        continue
                print("Could not decode text file with any supported encoding")
                return ""
            except Exception as e:
                print(f"Error reading text file: {e}")
                return ""
        else:
            print(f"Unsupported file extension: {ext}")
            return ""

    except Exception as e:
        print(f"Unexpected error in extract_text: {e}")
        return ""

def clean_text(text):
    text = re.sub(r'\n+', ' ', text)
    text = re.sub(r'[^A-Za-z0-9.,!?\'\"()\- ]+', '', text)
    text = re.sub(r'\s{2,}', ' ', text)
    return text.strip()

def text_similarity(text1, text2):
    """Calculate similarity between two texts using SequenceMatcher"""
    return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()

def simple_grammar_check(text):
    """Simple grammar and style checking without Java dependency"""
    issues = []
    sentences = re.split(r'[.!?]+', text)

    for i, sentence in enumerate(sentences):
        sentence = sentence.strip()
        if not sentence:
            continue

        # Check for common issues
        # 1. Sentence should start with capital letter
        if sentence and not sentence[0].isupper():
            issues.append({
                'ruleId': 'UPPERCASE_SENTENCE_START',
                'message': f'Sentence should start with a capital letter: "{sentence[:50]}..."',
                'replacements': [sentence.capitalize()],
                'sentence_num': i + 1
            })

        # 2. Check for double spaces
        if '  ' in sentence:
            issues.append({
                'ruleId': 'DOUBLE_SPACE',
                'message': f'Multiple spaces found in: "{sentence[:50]}..."',
                'replacements': [re.sub(r'\s+', ' ', sentence)],
                'sentence_num': i + 1
            })

        # 3. Check for missing space after punctuation
        if re.search(r'[,;:](?![,;:\s])', sentence):
            issues.append({
                'ruleId': 'MISSING_SPACE_AFTER_PUNCTUATION',
                'message': f'Missing space after punctuation in: "{sentence[:50]}..."',
                'replacements': [re.sub(r'([,;:])(?![,;:\s])', r'\1 ', sentence)],
                'sentence_num': i + 1
            })

    # 4. Check for common spelling mistakes (basic)
    common_mistakes = {
        'teh': 'the',
        'adn': 'and',
        'recieve': 'receive',
        'seperate': 'separate',
        'occured': 'occurred',
        'definately': 'definitely',
        'accomodate': 'accommodate'
    }

    words = re.findall(r'\b\w+\b', text.lower())
    for word in words:
        if word in common_mistakes:
            issues.append({
                'ruleId': 'COMMON_SPELLING_ERROR',
                'message': f'Possible spelling error: "{word}"',
                'replacements': [common_mistakes[word]],
                'sentence_num': 0
            })

    return issues

def check_plagiarism(text):
    """Check for potential plagiarism by comparing against known phrases"""
    known_phrases = [
        "To be or not to be",
        "The quick brown fox jumps over the lazy dog",
        "In conclusion",
        "As a result",
        "It is important to note that",
        "Furthermore, it should be noted",
        "According to the research",
        "The results indicate that"
    ]

    plagiarized = []
    text_lower = text.lower()

    for phrase in known_phrases:
        # Check if phrase exists in text or calculate similarity
        if phrase.lower() in text_lower:
            similarity = 100.0  # Exact match
        else:
            similarity = text_similarity(text, phrase) * 100

        if similarity > 20.0:  # Threshold for potential plagiarism
            plagiarized.append((phrase, similarity))

    return plagiarized

def correct_text_simple(text, issues):
    """Apply simple corrections to text"""
    corrected = text

    # Apply basic corrections
    for issue in issues:
        if issue['replacements'] and len(issue['replacements']) > 0:
            replacement = issue['replacements'][0]
            if issue['ruleId'] == 'DOUBLE_SPACE':
                corrected = re.sub(r'\s+', ' ', corrected)
            elif issue['ruleId'] == 'MISSING_SPACE_AFTER_PUNCTUATION':
                corrected = re.sub(r'([,;:])(?![,;:\s])', r'\1 ', corrected)
            elif issue['ruleId'] == 'COMMON_SPELLING_ERROR':
                # Simple word replacement
                pattern = r'\b' + re.escape(issue['message'].split('"')[1]) + r'\b'
                corrected = re.sub(pattern, replacement, corrected, flags=re.IGNORECASE)

    return corrected

# --- Global Storage ---
corrected_text = ""

# --- Error Handlers ---
@app.errorhandler(413)
def too_large(_):
    """Handle file too large error"""
    flash('File too large. Please upload a file smaller than 16MB.')
    return redirect(url_for('index'))

# --- Landing Page Template ---
LANDING_PAGE_TEMPLATE = '''
<!doctype html>
<html>
<head>
  <title>🌸 Thesis Checker - Beautiful AI Writing Assistant</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        overflow-x: hidden;
    }

    .hero-section {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        padding: 20px;
    }

    .hero-content {
        text-align: center;
        color: white;
        max-width: 800px;
        z-index: 2;
    }

    .hero-title {
        font-size: 4em;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
    }

    .hero-subtitle {
        font-size: 1.5em;
        margin-bottom: 30px;
        opacity: 0.9;
        animation: fadeInUp 1s ease-out 0.3s both;
    }

    .hero-description {
        font-size: 1.2em;
        margin-bottom: 40px;
        line-height: 1.6;
        animation: fadeInUp 1s ease-out 0.6s both;
    }

    .cta-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
        animation: fadeInUp 1s ease-out 0.9s both;
    }

    .cta-button {
        display: inline-block;
        padding: 20px 40px;
        background: linear-gradient(45deg, #ff6b6b, #feca57);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-size: 1.2em;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        position: relative;
        overflow: hidden;
    }

    .cta-button:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .cta-button:hover:before {
        left: 100%;
    }

    .cta-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        color: white;
        text-decoration: none;
    }

    .cta-button.secondary {
        background: linear-gradient(45deg, #48dbfb, #0abde3);
        box-shadow: 0 10px 30px rgba(72, 219, 251, 0.3);
    }

    .cta-button.secondary:hover {
        box-shadow: 0 15px 40px rgba(72, 219, 251, 0.4);
    }

    .features-section {
        background: rgba(255, 255, 255, 0.95);
        padding: 80px 20px;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .section-title {
        text-align: center;
        font-size: 3em;
        color: #4b0082;
        margin-bottom: 20px;
    }

    .section-subtitle {
        text-align: center;
        font-size: 1.3em;
        color: #666;
        margin-bottom: 60px;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        margin-bottom: 60px;
    }

    .feature-card {
        background: white;
        padding: 40px 30px;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: 3px solid transparent;
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    }

    .feature-card.grammar {
        border-color: #1dd1a1;
    }

    .feature-card.plagiarism {
        border-color: #ff6b6b;
    }

    .feature-card.punctuation {
        border-color: #feca57;
    }

    .feature-card.capitalization {
        border-color: #48dbfb;
    }

    .feature-icon {
        font-size: 4em;
        margin-bottom: 20px;
        display: block;
    }

    .feature-title {
        font-size: 1.8em;
        color: #2c3e50;
        margin-bottom: 15px;
        font-weight: bold;
    }

    .feature-description {
        color: #666;
        line-height: 1.6;
        font-size: 1.1em;
    }

    .stats-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 60px 20px;
        color: white;
        text-align: center;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 40px;
        max-width: 800px;
        margin: 0 auto;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 3em;
        font-weight: bold;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .stat-label {
        font-size: 1.2em;
        opacity: 0.9;
    }

    .footer {
        background: #2c3e50;
        color: white;
        padding: 40px 20px;
        text-align: center;
    }

    .footer-content {
        max-width: 800px;
        margin: 0 auto;
    }

    .footer-title {
        font-size: 2em;
        margin-bottom: 20px;
    }

    .footer-text {
        font-size: 1.1em;
        line-height: 1.6;
        opacity: 0.8;
    }

    .floating-flowers {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1;
    }

    .flower {
        position: absolute;
        font-size: 2em;
        opacity: 0.3;
        animation: float 6s ease-in-out infinite;
    }

    .flower:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
    .flower:nth-child(2) { top: 60%; left: 80%; animation-delay: 2s; }
    .flower:nth-child(3) { top: 80%; left: 20%; animation-delay: 4s; }
    .flower:nth-child(4) { top: 30%; left: 70%; animation-delay: 1s; }
    .flower:nth-child(5) { top: 70%; left: 50%; animation-delay: 3s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(10deg); }
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5em;
        }

        .hero-subtitle {
            font-size: 1.2em;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }

        .features-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
  </style>
</head>
<body>
  <div class="hero-section">
    <div class="floating-flowers">
      <div class="flower">🌸</div>
      <div class="flower">🌺</div>
      <div class="flower">🌻</div>
      <div class="flower">🌷</div>
      <div class="flower">🌹</div>
    </div>

    <div class="hero-content">
      <h1 class="hero-title">🌸 Thesis Checker 🌸</h1>
      <p class="hero-subtitle">Beautiful AI-Powered Writing Assistant</p>
      <p class="hero-description">
        Transform your academic writing with our stunning flower-themed thesis checker.
        Get professional grammar analysis, plagiarism detection, and writing suggestions
        in a beautiful, easy-to-use interface.
      </p>

      <div class="cta-buttons">
        <a href="/checker" class="cta-button">
          🌺 Start Checking Now 🌺
        </a>
        <a href="/about" class="cta-button secondary">
          🌿 Learn More 🌿
        </a>
      </div>
    </div>
  </div>

  <div class="features-section">
    <div class="container">
      <h2 class="section-title">🌺 Why Choose Our Thesis Checker?</h2>
      <p class="section-subtitle">Powerful features wrapped in a beautiful, intuitive interface</p>

      <div class="features-grid">
        <div class="feature-card grammar">
          <span class="feature-icon">🌿</span>
          <h3 class="feature-title">Advanced Grammar Checking</h3>
          <p class="feature-description">
            Comprehensive grammar and spelling analysis with intelligent suggestions.
            Uses LanguageTool for professional-grade checking.
          </p>
        </div>

        <div class="feature-card plagiarism">
          <span class="feature-icon">🌹</span>
          <h3 class="feature-title">Plagiarism Detection</h3>
          <p class="feature-description">
            Detect potential plagiarism and ensure originality in your academic work.
            Compare against common phrases and academic content.
          </p>
        </div>

        <div class="feature-card punctuation">
          <span class="feature-icon">🌻</span>
          <h3 class="feature-title">Punctuation Perfection</h3>
          <p class="feature-description">
            Perfect your punctuation with targeted analysis. Fix comma splices,
            missing periods, and other punctuation issues.
          </p>
        </div>

        <div class="feature-card capitalization">
          <span class="feature-icon">🌷</span>
          <h3 class="feature-title">Capitalization Correction</h3>
          <p class="feature-description">
            Ensure proper capitalization throughout your document.
            Check sentence beginnings, proper nouns, and title formatting.
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="stats-section">
    <div class="container">
      <h2 class="section-title">🌸 Trusted by Students Worldwide</h2>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">10K+</div>
          <div class="stat-label">Documents Checked</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">95%</div>
          <div class="stat-label">Accuracy Rate</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">50+</div>
          <div class="stat-label">Universities</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">24/7</div>
          <div class="stat-label">Available</div>
        </div>
      </div>
    </div>
  </div>

  <div class="footer">
    <div class="footer-content">
      <h3 class="footer-title">🌺 Ready to Perfect Your Thesis?</h3>
      <p class="footer-text">
        Join thousands of students who have improved their academic writing with our beautiful,
        AI-powered thesis checker. Start your journey to perfect writing today!
      </p>
    </div>
  </div>
</body>
</html>
'''

# --- Routes ---
@app.route("/")
def landing():
    """Beautiful landing page"""
    return render_template_string(LANDING_PAGE_TEMPLATE)

@app.route("/checker", methods=["GET", "POST"])
def index():
    global corrected_text
    issues = []
    plagiarized = []
    spelling = []
    punctuation = []
    capitalization = []
    file_uploaded = False
    filename = ""

    if request.method == "POST":
        try:
            action = request.form.get('action', 'upload')

            if action == 'upload':
                # Handle file upload
                if 'file' not in request.files:
                    flash('No file part')
                    return redirect(request.url)

                file = request.files['file']
                if file.filename == '':
                    flash('No selected file')
                    return redirect(request.url)

                if not allowed_file(file.filename):
                    flash('Invalid file type. Please upload .txt, .pdf, or .docx files only.')
                    return redirect(request.url)

                if file:
                    filename = secure_filename(file.filename)
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    file.save(filepath)

                    # Store file info in session or pass to template
                    file_uploaded = True
                    flash(f'✅ File "{filename}" uploaded successfully! Please select your analysis options.')

            elif action == 'analyze':
                # Handle analysis with selected options
                filename = request.form.get('filename', '')
                if not filename:
                    flash('No file selected for analysis')
                    return redirect(request.url)

                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                if not os.path.exists(filepath):
                    flash('File not found. Please upload again.')
                    return redirect(request.url)

                try:
                    raw = extract_text(filepath)
                    if not raw or len(raw.strip()) == 0:
                        flash('Could not extract text from file or file is empty')
                        return redirect(request.url)

                    clean = clean_text(raw)
                    if len(clean.strip()) == 0:
                        flash('No valid text found after cleaning')
                        return redirect(request.url)

                    # Get selected check options from checkboxes
                    check_grammar = request.form.get('check_grammar') == 'yes'
                    check_plagiarism = request.form.get('check_plagiarism') == 'yes'
                    check_punctuation = request.form.get('check_punctuation') == 'yes'
                    check_capitalization = request.form.get('check_capitalization') == 'yes'

                    # Perform checks based on selected options
                    if check_grammar:
                        # Check grammar using available tools
                        if LANGUAGE_TOOL_AVAILABLE:
                            try:
                                # Try to use LanguageTool with Java
                                tool = language_tool_python.LanguageTool('en-US')
                                matches = tool.check(clean)
                                corrected_text = language_tool_python.utils.correct(clean, matches)
                                tool.close()

                                # Categorize issues
                                for match in matches:
                                    if match.ruleId.startswith('MORFOLOGIK_'):
                                        spelling.append(match)
                                    elif any(p in match.ruleId for p in ['COMMA', 'PERIOD', 'COLON', 'SEMICOLON']):
                                        punctuation.append(match)
                                    elif 'UPPERCASE_SENTENCE_START' in match.ruleId or 'CASE_' in match.ruleId:
                                        capitalization.append(match)

                                issues = matches
                                flash(f'🌿 Grammar analysis complete! Found {len(issues)} issues using LanguageTool.')

                            except Exception as e:
                                error_msg = str(e).lower()
                                if 'java' in error_msg:
                                    flash('Java not detected. Using simple grammar checker instead.')
                                else:
                                    flash(f'LanguageTool error: {str(e)}. Using simple checker instead.')

                                # Fall back to simple checker
                                issues = simple_grammar_check(clean)
                                corrected_text = correct_text_simple(clean, issues)

                                # Categorize simple issues
                                for issue in issues:
                                    if issue['ruleId'] == 'COMMON_SPELLING_ERROR':
                                        spelling.append(issue)
                                    elif issue['ruleId'] in ['MISSING_SPACE_AFTER_PUNCTUATION', 'DOUBLE_SPACE']:
                                        punctuation.append(issue)
                                    elif issue['ruleId'] == 'UPPERCASE_SENTENCE_START':
                                        capitalization.append(issue)

                                flash(f'🌿 Found {len(issues)} issues using simple checker.')
                        else:
                            # Use simple grammar checker
                            issues = simple_grammar_check(clean)
                            corrected_text = correct_text_simple(clean, issues)

                            # Categorize simple issues
                            for issue in issues:
                                if issue['ruleId'] == 'COMMON_SPELLING_ERROR':
                                    spelling.append(issue)
                                elif issue['ruleId'] in ['MISSING_SPACE_AFTER_PUNCTUATION', 'DOUBLE_SPACE']:
                                    punctuation.append(issue)
                                elif issue['ruleId'] == 'UPPERCASE_SENTENCE_START':
                                    capitalization.append(issue)

                            flash(f'🌿 Grammar analysis complete! Found {len(issues)} issues using simple checker.')

                    if check_punctuation and not check_grammar:
                        # Only check punctuation if grammar wasn't already checked
                        punct_issues = simple_grammar_check(clean)
                        corrected_text = correct_text_simple(clean, punct_issues)

                        # Filter only punctuation issues
                        for issue in punct_issues:
                            if issue['ruleId'] in ['MISSING_SPACE_AFTER_PUNCTUATION', 'DOUBLE_SPACE']:
                                punctuation.append(issue)

                        flash(f'🌻 Punctuation check complete! Found {len(punctuation)} punctuation issues.')

                    if check_capitalization and not check_grammar:
                        # Only check capitalization if grammar wasn't already checked
                        cap_issues = simple_grammar_check(clean)
                        if not corrected_text:
                            corrected_text = correct_text_simple(clean, cap_issues)

                        # Filter only capitalization issues
                        for issue in cap_issues:
                            if issue['ruleId'] == 'UPPERCASE_SENTENCE_START':
                                capitalization.append(issue)

                        flash(f'🌷 Capitalization check complete! Found {len(capitalization)} capitalization issues.')

                    # Check plagiarism if requested
                    if check_plagiarism:
                        plagiarized = check_plagiarism(clean)
                        flash(f'🌹 Plagiarism check complete! Found {len(plagiarized)} potential matches.')

                    # Ensure we have some corrected text
                    if not corrected_text:
                        corrected_text = clean

                    # Clean up uploaded file
                    try:
                        os.remove(filepath)
                    except OSError:
                        pass  # File cleanup failed, but continue

                except Exception as e:
                    flash(f'Error processing file: {str(e)}')
                    return redirect(request.url)

        except Exception as e:
            flash(f'Upload error: {str(e)}')
            return redirect(request.url)

    return render_template_string(HTML_TEMPLATE,
                                 issues=issues,
                                 plagiarized=plagiarized,
                                 spelling=spelling,
                                 punctuation=punctuation,
                                 capitalization=capitalization,
                                 file_uploaded=file_uploaded,
                                 filename=filename)

@app.route("/download")
def download_file():
    global corrected_text
    if not corrected_text:
        return "No corrected text available for download.", 404
    path = os.path.join(app.config['UPLOAD_FOLDER'], "corrected_output.txt")
    with open(path, 'w', encoding='utf-8') as f:
        f.write(corrected_text)
    return send_file(path, as_attachment=True, mimetype='text/plain')

# Add a route for the about page
@app.route("/about")
def about():
    return render_template_string('''
    <!doctype html>
    <html>
    <head>
      <title>🌸 About Thesis Checker</title>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            padding: 30px;
            text-align: center;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .content {
            padding: 40px;
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        h2 {
            color: #4b0082;
            margin-bottom: 20px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #ffeef8 0%, #f0e6ff 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
            border: 2px solid #d63384;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .back-button {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            margin-top: 30px;
        }

        .back-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🌸 About Thesis Checker 🌸</h1>
        </div>

        <div class="content">
          <h2>🌺 Welcome to Your Beautiful Writing Assistant</h2>
          <p>Our flower-themed thesis checker helps students create perfect academic documents with AI-powered analysis.</p>

          <div class="feature-grid">
            <div class="feature-card">
              <div class="feature-icon">🌿</div>
              <h3>Grammar & Spelling</h3>
              <p>Advanced grammar checking with intelligent suggestions</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">🌻</div>
              <h3>Punctuation</h3>
              <p>Perfect punctuation for professional writing</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">🌷</div>
              <h3>Capitalization</h3>
              <p>Proper capitalization throughout your document</p>
            </div>

            <div class="feature-card">
              <div class="feature-icon">🌹</div>
              <h3>Plagiarism Detection</h3>
              <p>Ensure originality in your academic work</p>
            </div>
          </div>

          <div style="text-align: center;">
            <a href="/" class="back-button">� Back to Home 🏠</a>
            <a href="/checker" class="back-button" style="margin-left: 20px;">🌺 Go to Checker 🌺</a>
          </div>
        </div>
      </div>
    </body>
    </html>
    ''')

# --- Start Server ---
if __name__ == "__main__":
    print("=" * 60)
    print("🌸 THESIS CHECKER - Starting Application")
    print("=" * 60)

    # Check available features
    print("\n📋 Available Features:")
    print(f"   ✅ File Upload (.txt, .pdf, .docx)")
    print(f"   ✅ Basic Text Extraction")
    print(f"   ✅ Simple Plagiarism Detection")

    if LANGUAGE_TOOL_AVAILABLE:
        print(f"   ✅ Grammar Checking (LanguageTool available)")
        print(f"       Will attempt advanced checking, fallback to simple if Java unavailable")
    else:
        print(f"   ⚠️  Basic Grammar Checking (LanguageTool not available)")
        print(f"       Install language-tool-python for advanced checking")

    if NGROK_AVAILABLE:
        print(f"   ✅ Public Access Available (ngrok)")
        try:
            ngrok_token = "*************************************************"  # Replace with your token
            ngrok.set_auth_token(ngrok_token)
            public_url = ngrok.connect(5000)
            print(f"   🌐 Public URL: {public_url}")
        except Exception as e:
            print(f"   ❌ Ngrok setup failed: {e}")
            print(f"   📍 Running locally only")
    else:
        print(f"   📍 Local Access Only")
        print(f"       Install pyngrok for public access: pip install pyngrok")

    print(f"\n🚀 Starting Flask application...")
    print(f"📍 Local access: http://localhost:5000")
    print("=" * 60)

    # Start Flask app
    app.run(host='0.0.0.0', port=5000, debug=True)


